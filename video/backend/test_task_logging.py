#!/usr/bin/env python3
"""
测试任务日志功能的脚本
演示带时间戳的详细执行日志
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.tasks.task_logger import TaskLogger
import time
import random

def test_basic_logging():
    """测试基础日志功能"""
    print("=" * 80)
    print("测试基础日志功能")
    print("=" * 80)
    
    task_logger = TaskLogger("TEST_BASIC", task_id=1001, video_id=2001)
    task_logger.start_task(total_steps=5, description="基础日志功能测试")
    
    # 模拟各种步骤
    steps = [
        ("初始化环境", "设置必要的环境变量和配置"),
        ("加载数据", "从数据库加载必要的数据"),
        ("处理数据", "对数据进行复杂的处理操作"),
        ("生成结果", "生成最终的处理结果"),
        ("清理资源", "清理临时文件和释放资源")
    ]
    
    for i, (step_name, step_desc) in enumerate(steps):
        task_logger.start_step(step_name)
        
        # 模拟处理时间
        processing_time = random.uniform(0.5, 2.0)
        time.sleep(processing_time)
        
        # 记录性能信息
        task_logger.log_performance(step_name, processing_time, step_desc)
        
        # 模拟一些信息日志
        if i == 1:
            task_logger.log_info("数据加载详情", {
                "records_loaded": random.randint(100, 1000),
                "data_size_mb": f"{random.uniform(10, 100):.2f}"
            })
        elif i == 2:
            task_logger.log_progress(i+1, len(steps), "数据处理进度更新")
        
        task_logger.complete_step(step_name, f"步骤 {i+1} 完成")
    
    task_logger.complete_task(True, "基础日志功能测试完成")

def test_error_logging():
    """测试错误日志功能"""
    print("\n" + "=" * 80)
    print("测试错误日志功能")
    print("=" * 80)
    
    task_logger = TaskLogger("TEST_ERROR", task_id=1002, video_id=2002)
    task_logger.start_task(total_steps=3, description="错误日志功能测试")
    
    try:
        # 第一步成功
        task_logger.start_step("正常步骤")
        time.sleep(0.5)
        task_logger.complete_step("正常步骤", "这个步骤正常完成")
        
        # 第二步警告
        task_logger.start_step("警告步骤")
        time.sleep(0.3)
        task_logger.log_warning("这是一个警告信息", Exception("模拟警告异常"))
        task_logger.complete_step("警告步骤", "步骤完成但有警告")
        
        # 第三步错误
        task_logger.start_step("错误步骤")
        time.sleep(0.2)
        raise Exception("模拟的错误异常")
        
    except Exception as e:
        task_logger.log_error("步骤执行失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")

def test_video_analysis_simulation():
    """模拟视频分析任务的日志"""
    print("\n" + "=" * 80)
    print("模拟视频分析任务日志")
    print("=" * 80)
    
    task_logger = TaskLogger("VIDEO_ANALYSIS_SIMULATION", task_id=1003, video_id=2003)
    task_logger.start_task(total_steps=8, description="模拟视频分析任务")
    
    # 模拟视频分析的各个步骤
    analysis_steps = [
        ("获取视频信息", "从数据库获取视频基本信息", 0.2),
        ("检查系统依赖", "验证ffmpeg和ffprobe可用性", 0.1),
        ("分析视频元数据", "使用ffprobe提取视频元数据", 1.5),
        ("提取音频轨道", "从视频中提取音频文件", 2.0),
        ("生成自动字幕", "使用语音识别生成字幕", 3.0),
        ("提取关键帧", "提取视频关键帧图片", 1.8),
        ("场景检测", "检测视频场景切换点", 2.2),
        ("保存分析结果", "将结果保存到数据库", 0.3)
    ]
    
    for i, (step_name, step_desc, duration) in enumerate(analysis_steps):
        task_logger.start_step(f"{step_name} ({i+1}/8)")
        
        # 模拟处理过程
        time.sleep(duration)
        
        # 记录性能和结果信息
        task_logger.log_performance(step_name, duration, step_desc)
        
        # 模拟不同步骤的特定信息
        if step_name == "分析视频元数据":
            task_logger.log_info("元数据分析结果", {
                "duration": "120.5s",
                "resolution": "1920x1080",
                "fps": 30.0,
                "codec": "h264",
                "bitrate": "2500000 bps"
            })
        elif step_name == "提取音频轨道":
            task_logger.log_info("音频提取结果", {
                "audio_tracks": 2,
                "total_size_mb": "15.6"
            })
        elif step_name == "生成自动字幕":
            task_logger.log_info("字幕生成结果", {
                "segments": 45,
                "confidence": 0.87,
                "language": "zh-cn"
            })
        elif step_name == "提取关键帧":
            task_logger.log_info("关键帧提取结果", {
                "frames_extracted": 24,
                "total_size_mb": "8.9"
            })
        
        task_logger.complete_step(f"{step_name} ({i+1}/8)", f"步骤完成")
    
    task_logger.complete_task(True, "视频分析任务模拟完成")

def main():
    """主函数"""
    print("开始测试任务日志功能...")
    print("注意观察时间戳、执行时间和详细信息的记录")
    
    # 运行各种测试
    test_basic_logging()
    test_error_logging()
    test_video_analysis_simulation()
    
    print("\n" + "=" * 80)
    print("所有测试完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()