#!/usr/bin/env python3
"""
Test script to verify configuration models can be imported and used correctly
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

try:
    from app.models.configuration import (
        ConfigurationCategory, 
        ConfigurationItem, 
        ConfigurationHistory, 
        TaskConfiguration, 
        WorkerConfiguration
    )
    print("✓ Successfully imported all configuration models")
    
    # Test model attributes
    print("✓ ConfigurationCategory model has required fields:")
    print(f"  - name: {hasattr(ConfigurationCategory, 'name')}")
    print(f"  - display_name: {hasattr(ConfigurationCategory, 'display_name')}")
    print(f"  - description: {hasattr(ConfigurationCategory, 'description')}")
    print(f"  - icon: {hasattr(ConfigurationCategory, 'icon')}")
    print(f"  - sort_order: {hasattr(ConfigurationCategory, 'sort_order')}")
    
    print("✓ ConfigurationItem model has required fields:")
    print(f"  - key: {hasattr(ConfigurationItem, 'key')}")
    print(f"  - value_type: {hasattr(ConfigurationItem, 'value_type')}")
    print(f"  - validation_rules: {hasattr(ConfigurationItem, 'validation_rules')}")
    print(f"  - ui_component: {hasattr(ConfigurationItem, 'ui_component')}")
    
    print("✓ ConfigurationHistory model has required fields:")
    print(f"  - configuration_item_id: {hasattr(ConfigurationHistory, 'configuration_item_id')}")
    print(f"  - old_value: {hasattr(ConfigurationHistory, 'old_value')}")
    print(f"  - new_value: {hasattr(ConfigurationHistory, 'new_value')}")
    print(f"  - changed_by: {hasattr(ConfigurationHistory, 'changed_by')}")
    
    print("✓ TaskConfiguration model has required fields:")
    print(f"  - task_type: {hasattr(TaskConfiguration, 'task_type')}")
    print(f"  - parameters: {hasattr(TaskConfiguration, 'parameters')}")
    print(f"  - prompts: {hasattr(TaskConfiguration, 'prompts')}")
    
    print("✓ WorkerConfiguration model has required fields:")
    print(f"  - worker_name: {hasattr(WorkerConfiguration, 'worker_name')}")
    print(f"  - worker_type: {hasattr(WorkerConfiguration, 'worker_type')}")
    print(f"  - connection_params: {hasattr(WorkerConfiguration, 'connection_params')}")
    print(f"  - max_concurrency: {hasattr(WorkerConfiguration, 'max_concurrency')}")
    
    print("\n✅ All configuration models are working correctly!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)