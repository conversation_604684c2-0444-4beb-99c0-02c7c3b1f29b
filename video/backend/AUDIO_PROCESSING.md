# 音频处理和字幕生成功能

本文档介绍如何在视频分析系统中使用集成的音频处理和字幕生成功能。

## 功能概述

- **本地音频处理**: 不依赖外部服务，直接在本地进行语音识别
- **自动字幕生成**: 从视频音频轨道自动生成中文字幕
- **多格式支持**: 支持 WAV, MP3, FLAC, M4A, AAC, OGG, WMA 等音频格式
- **GPU 加速**: 自动检测并使用可用的 GPU 进行加速处理

## 安装依赖

### 1. 基础依赖

```bash
cd video/backend
pip install -r requirements.txt
```

### 2. 音频处理专用依赖

如果上述安装失败，可以单独安装音频处理依赖：

```bash
pip install GPUtil>=1.4.0
pip install soundfile>=0.12.0
pip install librosa>=0.10.0
pip install modelscope>=1.20.0
pip install huggingface_hub>=0.20.0
```

### 3. 验证安装

运行测试脚本验证安装是否成功：

```bash
cd video/backend
python test_audio_integration.py
```

## 配置

### 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 音频处理配置
ENABLE_PARSE_AUDIO=true
AUDIO_BATCH_SIZE=10
AUDIO_BATCH_SIZE_S=300
AUDIO_BATCH_SIZE_THRESHOLD_S=60
AUDIO_MAX_FILE_SIZE=100
AUDIO_TEMP_DIR=/tmp/audio_processing
AUDIO_CACHE_ENABLED=true
AUDIO_MAX_CONCURRENT_TASKS=3
```

### 配置说明

- `ENABLE_PARSE_AUDIO`: 是否启用音频处理功能
- `AUDIO_BATCH_SIZE`: 批处理大小
- `AUDIO_BATCH_SIZE_S`: 批处理时间长度（秒）
- `AUDIO_BATCH_SIZE_THRESHOLD_S`: 批处理阈值（秒）
- `AUDIO_MAX_FILE_SIZE`: 最大文件大小（MB）
- `AUDIO_TEMP_DIR`: 临时文件目录
- `AUDIO_CACHE_ENABLED`: 是否启用缓存
- `AUDIO_MAX_CONCURRENT_TASKS`: 最大并发任务数

## 使用方法

### 1. 检查服务状态

```bash
curl http://localhost:8000/api/v1/videos/audio-service/status
```

### 2. 自动字幕生成

字幕生成会在视频分析过程中自动进行，也可以单独调用：

```python
from app.services.subtitle_service import SubtitleService

# 创建字幕服务
subtitle_service = SubtitleService(db)

# 生成自动字幕
subtitle = subtitle_service.generate_automatic_subtitle(video_id)
```

### 3. API 调用

通过 REST API 生成字幕：

```bash
# 生成字幕
POST /api/v1/videos/{video_id}/subtitles/generate
```

## 模型信息

### 使用的模型

- **语音识别模型**: `iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch`
- **VAD 模型**: `iic/speech_fsmn_vad_zh-cn-16k-common-pytorch`
- **标点符号模型**: `iic/punc_ct-transformer_cn-en-common-vocab471067-large`
- **语言模型**: `iic/speech_transformer_lm_zh-cn-common-vocab8404-pytorch`

### 模型特点

- 支持中文语音识别
- 自动语音活动检测 (VAD)
- 自动标点符号添加
- 支持长音频处理
- 提供时间戳信息

## 故障排除

### 1. 依赖安装问题

如果遇到依赖安装问题：

```bash
# 更新 pip
pip install --upgrade pip

# 清理缓存
pip cache purge

# 重新安装
pip install -r requirements.txt --force-reinstall
```

### 2. GPU 相关问题

如果 GPU 不可用，系统会自动回退到 CPU 模式：

```bash
# 检查 GPU 状态
python -c "import GPUtil; print(GPUtil.getGPUs())"
```

### 3. 模型下载问题

如果模型下载失败，可以手动设置镜像：

```bash
export HF_ENDPOINT=https://hf-mirror.com
```

### 4. 内存不足

如果遇到内存不足问题，可以调整批处理大小：

```env
AUDIO_BATCH_SIZE=5
AUDIO_BATCH_SIZE_S=150
```

## 性能优化

### 1. GPU 加速

确保安装了正确的 PyTorch 版本以支持 GPU：

```bash
# CUDA 版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# CPU 版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

### 2. 并发处理

调整并发任务数以优化性能：

```env
AUDIO_MAX_CONCURRENT_TASKS=2  # 根据系统资源调整
```

### 3. 缓存优化

启用缓存以提高重复处理的速度：

```env
AUDIO_CACHE_ENABLED=true
```

## 开发和调试

### 1. 日志配置

音频处理的日志会输出到系统日志中，可以通过以下方式查看：

```python
from loguru import logger
logger.add("audio_processing.log", rotation="1 day")
```

### 2. 测试音频处理

使用测试脚本验证功能：

```bash
python test_audio_integration.py
```

### 3. 手动测试

```python
from app.services.local_audio_service import local_audio_service

# 检查服务状态
print(local_audio_service.is_available())

# 处理音频文件
result = await local_audio_service.process_audio("/path/to/audio.wav")
print(result)
```

## 注意事项

1. **首次使用**: 首次使用时会下载模型文件，可能需要较长时间
2. **网络要求**: 模型下载需要稳定的网络连接
3. **存储空间**: 模型文件较大，确保有足够的存储空间
4. **系统资源**: 音频处理需要较多的 CPU/GPU 资源
5. **文件格式**: 确保音频文件格式受支持

## 更新和维护

### 1. 更新模型

模型会自动更新，如需手动更新：

```bash
pip install --upgrade modelscope
```

### 2. 清理缓存

定期清理临时文件：

```bash
rm -rf /tmp/audio_processing/*
```

### 3. 监控性能

通过 API 监控处理性能：

```bash
curl http://localhost:8000/api/v1/videos/audio-service/status
```
