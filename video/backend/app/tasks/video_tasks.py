"""
视频处理相关的Celery任务
"""

from celery import current_task, chain, group
from app.tasks.celery import celery
from app.core.database import SessionLocal
from app.models.task import Video, AnalysisResult, Task
from app.services.task_service import TaskService
from app.services.video_analysis_service import VideoAnalysisService
from app.services.subtitle_service import SubtitleService
from app.services.ffmpeg_service import ffmpeg_service
from app.services.scene_detection_service import SceneDetectionService
from sqlalchemy.orm import Session
import os
import time
import json
from datetime import datetime
from loguru import logger
from .video_tasks_basic_info import analyze_video_basic_info
from .video_tasks_content import analyze_video_content
from .video_tasks_plot import analyze_video_plot
from .video_tasks_minicpm import (
    analyze_video_with_minicpm_scenes,
    detect_scene_changes_with_minicpm,
    generate_video_content_summary_with_minicpm,
    comprehensive_minicpm_analysis,
    comprehensive_minicpm_analysis_with_subtitles
)
from .task_utils import safe_update_task_state
from .task_logger import TaskLogger


# 主要处理逻辑
@celery.task(bind=True)
def process_task_videos(self, task_id: int):
    """处理任务中的所有视频 - 任务编排主入口"""
    task_logger = TaskLogger("TASK_VIDEOS", task_id=task_id)
    task_logger.start_task(description=f"处理任务 {task_id} 中的所有视频")
    
    db = SessionLocal()
    try:
        # 步骤1: 获取任务信息
        task_logger.start_step("获取任务信息")
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise Exception(f"Task {task_id} not found")
        task_logger.complete_step("获取任务信息", f"任务名称: {task.name}")

        # 步骤2: 检查任务状态
        task_logger.start_step("检查任务状态")
        if not check_task_status(task_id, db):
            task_logger.log_warning("任务已被暂停或取消")
            task_logger.complete_task(False, "任务已暂停")
            return {
                'status': 'paused',
                'task_id': task_id,
                'message': '任务已暂停'
            }
        task_logger.complete_step("检查任务状态", "任务状态正常")

        # 步骤3: 获取视频列表
        task_logger.start_step("获取视频列表")
        videos = db.query(Video).filter(Video.task_id == task_id).all()
        if not videos:
            raise Exception(f"No videos found for task {task_id}")
        task_logger.complete_step("获取视频列表", f"找到 {len(videos)} 个视频文件")

        # 步骤4: 初始化任务状态
        task_logger.start_step("初始化任务状态")
        task_service = TaskService(db)
        task_service.set_status(task_id, "processing")
        task_service.update_progress(task_id, 0.0)

        current_task.update_state(
            state='PROGRESS',
            meta={
                'current': 0,
                'total': len(videos),
                'status': f'开始处理任务中的 {len(videos)} 个视频...'
            }
        )
        task_logger.complete_step("初始化任务状态", "任务状态已设置为处理中")

        # 步骤5: 创建视频分析任务链
        task_logger.start_step("创建视频分析任务链")
        video_tasks = []
        for i, video in enumerate(videos):
            # 更新视频状态
            video.status = "analyzing"
            task_logger.log_info(f"为视频 {video.id} ({video.filename}) 创建分析任务链")

            # 创建视频分析任务链：基础信息 -> 内容分析 -> 剧情分析
            video_chain = chain(
                analyze_video_basic_info.s(video.id),
                analyze_video_content.si(video.id),
                analyze_video_plot.si(video.id)
            )
            video_tasks.append(video_chain)

        db.commit()
        task_logger.complete_step("创建视频分析任务链", f"创建了 {len(video_tasks)} 个任务链")

        # 步骤6: 并行执行分析任务
        task_logger.start_step("并行执行分析任务")
        job = group(video_tasks)
        result = job.apply_async()
        task_logger.log_info("所有视频分析任务已提交到队列")

        # 步骤7: 监控任务执行进度
        task_logger.start_step("监控任务执行进度")
        completed_count = 0
        total_videos = len(videos)
        check_interval = 2  # 检查间隔2秒

        while not result.ready():
            time.sleep(check_interval)
            logger.debug("Checking task status...")

            # 检查任务是否被暂停
            if not check_task_status(task_id, db):
                task_logger.log_warning("检测到任务被暂停，正在停止执行")
                # 撤销所有子任务
                result.revoke(terminate=True)
                task_service.set_status(task_id, "paused")
                task_logger.complete_task(False, "任务被用户暂停")
                return {
                    'status': 'paused',
                    'task_id': task_id,
                    'message': '任务已暂停'
                }

            # 检查已完成的视频数量
            completed_videos = db.query(Video).filter(
                Video.task_id == task_id,
                Video.status == "analyzed"
            ).count()

            if completed_videos > completed_count:
                completed_count = completed_videos
                progress = (completed_count / total_videos) * 100

                task_service.update_progress(task_id, progress)
                current_task.update_state(
                    state='PROGRESS',
                    meta={
                        'current': completed_count,
                        'total': total_videos,
                        'status': f'已完成 {completed_count}/{total_videos} 个视频的分析'
                    }
                )
                task_logger.log_progress(completed_count, total_videos, f"视频分析进度更新")

        task_logger.complete_step("监控任务执行进度", "所有任务执行完毕")

        # 步骤8: 检查执行结果
        task_logger.start_step("检查执行结果")
        failed_videos = db.query(Video).filter(
            Video.task_id == task_id,
            Video.status == "failed"
        ).count()

        successful_videos = db.query(Video).filter(
            Video.task_id == task_id,
            Video.status == "analyzed"
        ).count()

        task_logger.log_info(f"执行结果统计: 成功 {successful_videos} 个, 失败 {failed_videos} 个")

        if failed_videos > 0:
            # 有失败的视频
            task_service.set_status(task_id, "failed")
            task_service.update_progress(task_id, 100.0)

            current_task.update_state(
                state='FAILURE',
                meta={'error': f'{failed_videos} 个视频分析失败'}
            )
            task_logger.complete_step("检查执行结果", f"{failed_videos} 个视频分析失败")
            task_logger.complete_task(False, f"任务部分失败: {failed_videos}/{total_videos} 个视频失败")
            raise Exception(f'{failed_videos} 个视频分析失败')
        else:
            # 所有视频都成功完成
            task_service.set_status(task_id, "completed")
            task_service.update_progress(task_id, 100.0)
            task_logger.complete_step("检查执行结果", "所有视频分析成功")
            task_logger.complete_task(True, f"成功完成 {total_videos} 个视频的分析")

            return {
                'status': 'completed',
                'task_id': task_id,
                'videos_processed': total_videos,
                'message': f'成功完成 {total_videos} 个视频的分析'
            }

    except Exception as e:
        # 错误处理
        task_logger.log_error("任务执行异常", e)
        task_service = TaskService(db)
        task_service.set_status(task_id, "failed")

        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        task_logger.complete_task(False, f"任务执行失败: {str(e)}")
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def process_single_video_complete_analysis(self, video_id: int):
    """处理单个视频的完整分析流程"""
    task_logger = TaskLogger("SINGLE_VIDEO_ANALYSIS", video_id=video_id)
    task_logger.start_task(total_steps=5, description=f"单个视频 {video_id} 完整分析流程")
    
    db = SessionLocal()
    try:
        # 步骤1: 获取视频信息
        task_logger.start_step("获取视频信息")
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")
        task_logger.complete_step("获取视频信息", f"视频文件: {video.filename}")

        # 步骤2: 更新视频状态
        task_logger.start_step("更新视频状态")
        video.status = "analyzing"
        db.commit()
        task_logger.complete_step("更新视频状态", "状态已设置为分析中")

        current_task.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 3, 'status': '开始视频分析...'}
        )

        # 步骤3: 创建分析任务链
        task_logger.start_step("创建分析任务链")
        analysis_chain = chain(
            analyze_video_basic_info.s(video_id),
            analyze_video_content.si(video_id),
            analyze_video_plot.si(video_id)
        )
        task_logger.complete_step("创建分析任务链", "基础信息 -> 内容分析 -> 剧情分析")

        # 步骤4: 执行分析链
        task_logger.start_step("执行分析链")
        result = analysis_chain.apply_async()
        task_logger.log_info("分析任务已提交到队列")

        # 监控执行进度
        check_count = 0
        while not result.ready():
            time.sleep(1)
            check_count += 1
            if check_count % 10 == 0:  # 每10秒记录一次
                task_logger.log_info(f"分析任务执行中... (已等待 {check_count} 秒)")

        task_logger.complete_step("执行分析链", "所有分析任务执行完毕")

        # 步骤5: 检查结果
        task_logger.start_step("检查分析结果")
        if result.successful():
            task_logger.complete_step("检查分析结果", "所有分析步骤成功完成")
            task_logger.complete_task(True, "视频分析完成")
            return {
                'status': 'completed',
                'video_id': video_id,
                'message': '视频分析完成'
            }
        else:
            error_info = str(result.result) if hasattr(result, 'result') else "未知错误"
            task_logger.complete_step("检查分析结果", f"分析失败: {error_info}")
            task_logger.complete_task(False, f"视频分析失败: {error_info}")
            raise Exception(f'视频分析失败: {error_info}')

    except Exception as e:
        # 更新视频状态为失败
        task_logger.log_error("分析过程中发生异常", e)
        video = db.query(Video).filter(Video.id == video_id).first()
        if video:
            video.status = "failed"
            db.commit()
            task_logger.log_info("视频状态已更新为失败")

        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        task_logger.complete_task(False, f"视频分析异常: {str(e)}")
        raise e
    finally:
        db.close()

def check_task_status(task_id: int, db: Session) -> bool:
    """检查任务状态，如果被暂停则返回False"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        return False
    return task.status not in ["paused", "cancelled"]


@celery.task(bind=True)
def process_single_video_with_minicpm_analysis(self, video_id: int):
    """处理单个视频的完整分析流程（包含MiniCPM-V-4）"""
    task_logger = TaskLogger("SINGLE_VIDEO_MINICPM", video_id=video_id)
    task_logger.start_task(total_steps=6, description=f"单个视频 {video_id} 完整分析流程（含MiniCPM-V-4）")
    
    db = SessionLocal()
    try:
        # 步骤1: 获取视频信息
        task_logger.start_step("获取视频信息")
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")
        task_logger.complete_step("获取视频信息", f"视频文件: {video.filename}")

        # 步骤2: 更新视频状态
        task_logger.start_step("更新视频状态")
        video.status = "analyzing"
        db.commit()
        task_logger.complete_step("更新视频状态", "状态已设置为分析中")

        current_task.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 4, 'status': '开始视频分析...'}
        )

        # 步骤3: 创建传统分析任务链
        task_logger.start_step("创建传统分析任务链")
        traditional_analysis_chain = chain(
            analyze_video_basic_info.s(video_id),
            analyze_video_content.si(video_id),
            analyze_video_plot.si(video_id)
        )
        task_logger.complete_step("创建传统分析任务链", "基础信息 -> 内容分析 -> 剧情分析")

        # 步骤4: 执行传统分析链
        task_logger.start_step("执行传统分析链")
        traditional_result = traditional_analysis_chain.apply_async()
        task_logger.log_info("传统分析任务已提交到队列")

        # 监控传统分析执行进度
        check_count = 0
        while not traditional_result.ready():
            time.sleep(1)
            check_count += 1
            if check_count % 10 == 0:  # 每10秒记录一次
                task_logger.log_info(f"传统分析任务执行中... (已等待 {check_count} 秒)")

        if not traditional_result.successful():
            error_info = str(traditional_result.result) if hasattr(traditional_result, 'result') else "未知错误"
            raise Exception(f'传统分析失败: {error_info}')
        
        task_logger.complete_step("执行传统分析链", "传统分析任务执行完毕")

        # 步骤5: 执行MiniCPM-V-4综合分析（优先使用字幕集成版本）
        task_logger.start_step("执行MiniCPM-V-4综合分析")
        minicpm_result = comprehensive_minicpm_analysis_with_subtitles.apply_async(args=[video_id])
        task_logger.log_info("MiniCPM-V-4分析任务（含字幕集成）已提交到队列")

        # 监控MiniCPM-V-4分析执行进度
        check_count = 0
        while not minicpm_result.ready():
            time.sleep(2)  # MiniCPM-V-4分析可能需要更长时间
            check_count += 1
            if check_count % 15 == 0:  # 每30秒记录一次
                task_logger.log_info(f"MiniCPM-V-4分析任务执行中... (已等待 {check_count * 2} 秒)")

        if not minicpm_result.successful():
            error_info = str(minicpm_result.result) if hasattr(minicpm_result, 'result') else "未知错误"
            task_logger.log_warning(f"MiniCPM-V-4分析失败: {error_info}")
            # MiniCPM-V-4分析失败不影响整体流程
        else:
            task_logger.complete_step("执行MiniCPM-V-4综合分析", "MiniCPM-V-4分析任务执行完毕")

        # 步骤6: 检查最终结果
        task_logger.start_step("检查最终结果")
        video = db.query(Video).filter(Video.id == video_id).first()
        video.status = "analyzed"
        db.commit()
        
        task_logger.complete_step("检查最终结果", "所有分析步骤完成")
        task_logger.complete_task(True, "视频完整分析完成（含MiniCPM-V-4）")
        
        return {
            'status': 'completed',
            'video_id': video_id,
            'traditional_analysis': traditional_result.successful(),
            'minicpm_analysis': minicpm_result.successful() if 'minicpm_result' in locals() else False,
            'message': '视频完整分析完成（含MiniCPM-V-4）'
        }

    except Exception as e:
        # 更新视频状态为失败
        task_logger.log_error("分析过程中发生异常", e)
        video = db.query(Video).filter(Video.id == video_id).first()
        if video:
            video.status = "failed"
            db.commit()
            task_logger.log_info("视频状态已更新为失败")

        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        task_logger.complete_task(False, f"视频分析异常: {str(e)}")
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def process_task_videos_with_minicpm(self, task_id: int):
    """处理任务中的所有视频（包含MiniCPM-V-4分析）"""
    task_logger = TaskLogger("TASK_VIDEOS_MINICPM", task_id=task_id)
    task_logger.start_task(description=f"处理任务 {task_id} 中的所有视频（含MiniCPM-V-4）")
    
    db = SessionLocal()
    try:
        # 步骤1: 获取任务信息
        task_logger.start_step("获取任务信息")
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise Exception(f"Task {task_id} not found")
        task_logger.complete_step("获取任务信息", f"任务名称: {task.name}")

        # 步骤2: 检查任务状态
        task_logger.start_step("检查任务状态")
        if not check_task_status(task_id, db):
            task_logger.log_warning("任务已被暂停或取消")
            task_logger.complete_task(False, "任务已暂停")
            return {
                'status': 'paused',
                'task_id': task_id,
                'message': '任务已暂停'
            }
        task_logger.complete_step("检查任务状态", "任务状态正常")

        # 步骤3: 获取视频列表
        task_logger.start_step("获取视频列表")
        videos = db.query(Video).filter(Video.task_id == task_id).all()
        if not videos:
            raise Exception(f"No videos found for task {task_id}")
        task_logger.complete_step("获取视频列表", f"找到 {len(videos)} 个视频文件")

        # 步骤4: 初始化任务状态
        task_logger.start_step("初始化任务状态")
        task_service = TaskService(db)
        task_service.set_status(task_id, "processing")
        task_service.update_progress(task_id, 0.0)

        current_task.update_state(
            state='PROGRESS',
            meta={
                'current': 0,
                'total': len(videos),
                'status': f'开始处理任务中的 {len(videos)} 个视频（含MiniCPM-V-4）...'
            }
        )
        task_logger.complete_step("初始化任务状态", "任务状态已设置为处理中")

        # 步骤5: 创建视频分析任务链（包含MiniCPM-V-4）
        task_logger.start_step("创建视频分析任务链")
        video_tasks = []
        for i, video in enumerate(videos):
            # 更新视频状态
            video.status = "analyzing"
            task_logger.log_info(f"为视频 {video.id} ({video.filename}) 创建完整分析任务链")

            # 创建包含MiniCPM-V-4的完整分析任务
            video_task = process_single_video_with_minicpm_analysis.s(video.id)
            video_tasks.append(video_task)

        db.commit()
        task_logger.complete_step("创建视频分析任务链", f"创建了 {len(video_tasks)} 个完整分析任务")

        # 步骤6: 并行执行分析任务
        task_logger.start_step("并行执行分析任务")
        job = group(video_tasks)
        result = job.apply_async()
        task_logger.log_info("所有视频分析任务已提交到队列")

        # 步骤7: 监控任务执行进度
        task_logger.start_step("监控任务执行进度")
        completed_count = 0
        total_videos = len(videos)
        check_interval = 5  # 检查间隔5秒（MiniCPM-V-4分析需要更长时间）

        while not result.ready():
            time.sleep(check_interval)

            # 检查任务是否被暂停
            if not check_task_status(task_id, db):
                task_logger.log_warning("检测到任务被暂停，正在停止执行")
                # 撤销所有子任务
                result.revoke(terminate=True)
                task_service.set_status(task_id, "paused")
                task_logger.complete_task(False, "任务被用户暂停")
                return {
                    'status': 'paused',
                    'task_id': task_id,
                    'message': '任务已暂停'
                }

            # 检查已完成的视频数量
            completed_videos = db.query(Video).filter(
                Video.task_id == task_id,
                Video.status == "analyzed"
            ).count()

            if completed_videos > completed_count:
                completed_count = completed_videos
                progress = (completed_count / total_videos) * 100

                task_service.update_progress(task_id, progress)
                current_task.update_state(
                    state='PROGRESS',
                    meta={
                        'current': completed_count,
                        'total': total_videos,
                        'status': f'已完成 {completed_count}/{total_videos} 个视频的完整分析（含MiniCPM-V-4）'
                    }
                )
                task_logger.log_progress(completed_count, total_videos, f"视频分析进度更新")

        task_logger.complete_step("监控任务执行进度", "所有任务执行完毕")

        # 步骤8: 检查执行结果
        task_logger.start_step("检查执行结果")
        failed_videos = db.query(Video).filter(
            Video.task_id == task_id,
            Video.status == "failed"
        ).count()

        successful_videos = db.query(Video).filter(
            Video.task_id == task_id,
            Video.status == "analyzed"
        ).count()

        task_logger.log_info(f"执行结果统计: 成功 {successful_videos} 个, 失败 {failed_videos} 个")

        if failed_videos > 0:
            # 有失败的视频
            task_service.set_status(task_id, "failed")
            task_service.update_progress(task_id, 100.0)

            current_task.update_state(
                state='FAILURE',
                meta={'error': f'{failed_videos} 个视频分析失败'}
            )
            task_logger.complete_step("检查执行结果", f"{failed_videos} 个视频分析失败")
            task_logger.complete_task(False, f"任务部分失败: {failed_videos}/{total_videos} 个视频失败")
            raise Exception(f'{failed_videos} 个视频分析失败')
        else:
            # 所有视频都成功完成
            task_service.set_status(task_id, "completed")
            task_service.update_progress(task_id, 100.0)
            task_logger.complete_step("检查执行结果", "所有视频分析成功")
            task_logger.complete_task(True, f"成功完成 {total_videos} 个视频的完整分析（含MiniCPM-V-4）")

            return {
                'status': 'completed',
                'task_id': task_id,
                'videos_processed': total_videos,
                'message': f'成功完成 {total_videos} 个视频的完整分析（含MiniCPM-V-4）'
            }

    except Exception as e:
        # 错误处理
        task_logger.log_error("任务执行异常", e)
        task_service = TaskService(db)
        task_service.set_status(task_id, "failed")

        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        task_logger.complete_task(False, f"任务执行失败: {str(e)}")
        raise e
    finally:
        db.close()